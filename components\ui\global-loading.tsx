"use client"

import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Zap, Cpu, Brain, Target, Loader2 } from 'lucide-react'

interface LandingPageLoadingProps {
  isVisible: boolean
  currentSection?: string
  progress?: number
  variant?: 'section' | 'full-page' | 'minimal'
}

interface QuantumParticle {
  id: number
  x: number
  y: number
  delay: number
  color: string
  size: number
}

const quantumColors = {
  cyan: '#22d3ee',
  purple: '#8b5cf6',
  gold: '#fbbf24'
}

const sectionMessages = {
  'hero': 'Materializing quantum arrival...',
  'features': 'Loading feature showcase...',
  'decode': 'Initializing DeCode learning...',
  'journey': 'Preparing game journey...',
  'byteverse': 'Loading Byteverse map...',
  'community': 'Connecting to community...',
  'testimonials': 'Loading testimonials...',
  'cta': 'Preparing call to action...',
  'default': 'Loading NanoVerse experience...'
}

const quantumIcons = [
  { Icon: Zap, color: quantumColors.cyan, label: 'Neural Core' },
  { Icon: Cpu, color: quantumColors.purple, label: 'Quantum AI' },
  { Icon: Brain, color: quantumColors.gold, label: 'Consciousness' },
  { Icon: Target, color: quantumColors.cyan, label: 'Precision' }
]

export function LandingPageLoading({
  isVisible,
  currentSection = 'default',
  progress,
  variant = 'section'
}: LandingPageLoadingProps) {
  const [currentMessage, setCurrentMessage] = useState(sectionMessages[currentSection as keyof typeof sectionMessages] || sectionMessages.default)
  const [particles, setParticles] = useState<QuantumParticle[]>([])
  const [currentIconIndex, setCurrentIconIndex] = useState(0)

  // Update message when section changes
  useEffect(() => {
    setCurrentMessage(sectionMessages[currentSection as keyof typeof sectionMessages] || sectionMessages.default)
  }, [currentSection])

  // Generate quantum particles
  useEffect(() => {
    if (!isVisible) return

    const generateParticles = () => {
      const newParticles: QuantumParticle[] = []
      const colors = Object.values(quantumColors)
      const particleCount = variant === 'minimal' ? 8 : variant === 'section' ? 12 : 20

      for (let i = 0; i < particleCount; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          delay: Math.random() * 3,
          color: colors[Math.floor(Math.random() * colors.length)],
          size: Math.random() * 3 + 1
        })
      }
      setParticles(newParticles)
    }

    generateParticles()
  }, [isVisible, variant])

  // Cycle through icons
  useEffect(() => {
    if (!isVisible) return

    const interval = setInterval(() => {
      setCurrentIconIndex(prev => (prev + 1) % quantumIcons.length)
    }, 1500)

    return () => clearInterval(interval)
  }, [isVisible])

  // Minimal loader for quick transitions
  if (variant === 'minimal') {
    return (
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm"
          >
            <div className="flex items-center gap-3 quantum-glass rounded-lg px-4 py-3">
              <Loader2 className="w-5 h-5 text-neural-cyan animate-spin" />
              <span className="text-white font-space-grotesk text-sm">
                {currentMessage}
              </span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    )
  }

  // Section loader for individual landing page sections
  if (variant === 'section') {
    return (
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="flex flex-col items-center justify-center p-8 min-h-[400px] relative"
          >
            {/* Consciousness wave background */}
            <div className="absolute inset-0 consciousness-wave opacity-20 rounded-xl" />

            {/* Quantum particles */}
            <div className="absolute inset-0 overflow-hidden rounded-xl">
              {particles.map((particle) => (
                <motion.div
                  key={particle.id}
                  className="absolute rounded-full"
                  style={{
                    left: `${particle.x}%`,
                    top: `${particle.y}%`,
                    width: `${particle.size}px`,
                    height: `${particle.size}px`,
                    backgroundColor: particle.color,
                    boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`
                  }}
                  animate={{
                    opacity: [0.3, 0.8, 0.3],
                    scale: [0.8, 1.2, 0.8],
                    x: [0, Math.random() * 10 - 5, 0],
                    y: [0, Math.random() * 10 - 5, 0]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: particle.delay,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </div>

            {/* Loading content */}
            <div className="relative z-10 text-center">
              {/* Quantum icon */}
              <motion.div
                key={currentIconIndex}
                initial={{ scale: 0, rotate: -90 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.5 }}
                className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center quantum-glow"
                style={{
                  background: `linear-gradient(135deg, ${quantumIcons[currentIconIndex].color}30, ${quantumIcons[currentIconIndex].color}10)`,
                  border: `2px solid ${quantumIcons[currentIconIndex].color}`,
                }}
              >
                <quantumIcons[currentIconIndex].Icon
                  className="w-8 h-8"
                  style={{ color: quantumIcons[currentIconIndex].color }}
                />
              </motion.div>

              {/* Loading text */}
              <motion.p
                key={currentMessage}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-white/90 font-space-grotesk text-lg mb-2"
              >
                {currentMessage}
              </motion.p>

              {/* Progress bar (if provided) */}
              {typeof progress === 'number' && (
                <div className="w-48 mx-auto mt-4">
                  <div className="h-1 bg-white/20 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-neural-cyan to-quantum-purple"
                      initial={{ width: 0 }}
                      animate={{ width: `${progress}%` }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    )
  }

  // Full page loader for initial app loading
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center"
          style={{
            background: 'linear-gradient(135deg, #0f0f23 0%, #1e1b4b 50%, #0f0f23 100%)'
          }}
        >
          {/* Consciousness wave background */}
          <div className="absolute inset-0 consciousness-wave opacity-30" />

          {/* Quantum particles */}
          <div className="absolute inset-0 overflow-hidden">
            {particles.map((particle) => (
              <motion.div
                key={particle.id}
                className="absolute rounded-full quantum-particle"
                style={{
                  left: `${particle.x}%`,
                  top: `${particle.y}%`,
                  width: `${particle.size}px`,
                  height: `${particle.size}px`,
                  backgroundColor: particle.color,
                  boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`
                }}
                animate={{
                  opacity: [0.3, 1, 0.3],
                  scale: [0.8, 1.2, 0.8],
                  x: [0, Math.random() * 20 - 10, 0],
                  y: [0, Math.random() * 20 - 10, 0]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: particle.delay,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>

          {/* Main loading content */}
          <div className="relative z-10 text-center">
            {/* Quantum energy rings */}
            <div className="relative mb-8">
              {/* Outer ring */}
              <motion.div
                className="w-32 h-32 rounded-full border-2 border-neural-cyan/30"
                animate={{ rotate: 360 }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              />
              
              {/* Middle ring */}
              <motion.div
                className="absolute inset-2 rounded-full border-2 border-quantum-purple/40"
                animate={{ rotate: -360 }}
                transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
              />
              
              {/* Inner ring */}
              <motion.div
                className="absolute inset-4 rounded-full border-2 border-quantum-gold/50"
                animate={{ rotate: 360 }}
                transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
              />

              {/* Central icon */}
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  key={currentIconIndex}
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  exit={{ scale: 0, rotate: 180 }}
                  transition={{ duration: 0.5 }}
                  className="w-12 h-12 rounded-full flex items-center justify-center quantum-glow"
                  style={{
                    background: `linear-gradient(135deg, ${quantumIcons[currentIconIndex].color}30, ${quantumIcons[currentIconIndex].color}10)`,
                    border: `2px solid ${quantumIcons[currentIconIndex].color}`,
                  }}
                >
                  <quantumIcons[currentIconIndex].Icon 
                    className="w-6 h-6" 
                    style={{ color: quantumIcons[currentIconIndex].color }}
                  />
                </motion.div>
              </div>
            </div>

            {/* Loading text */}
            <motion.div
              key={currentMessage}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.5 }}
              className="mb-6"
            >
              <h2 className="text-2xl font-orbitron font-bold bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent mb-2">
                NanoVerse Loading
              </h2>
              <p className="text-white/80 font-space-grotesk text-sm">
                {currentMessage}
              </p>
            </motion.div>

            {/* Progress bar (if provided) */}
            {typeof progress === 'number' && (
              <div className="w-64 mx-auto">
                <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold"
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
                <p className="text-white/60 text-xs mt-2 font-space-grotesk">
                  {Math.round(progress)}% Complete
                </p>
              </div>
            )}

            {/* Quantum status indicators */}
            <div className="flex justify-center gap-4 mt-8">
              {quantumIcons.map((item, index) => (
                <motion.div
                  key={index}
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                  animate={{
                    opacity: index === currentIconIndex ? [0.5, 1, 0.5] : 0.3,
                    scale: index === currentIconIndex ? [1, 1.2, 1] : 1
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: index === currentIconIndex ? Infinity : 0
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Hook for managing landing page section loading
export function useLandingPageLoading() {
  const [isLoading, setIsLoading] = useState(false)
  const [currentSection, setCurrentSection] = useState<string>('default')
  const [progress, setProgress] = useState<number>()
  const [variant, setVariant] = useState<'section' | 'full-page' | 'minimal'>('section')

  const showSectionLoading = (section: string, loadingProgress?: number, loadingVariant: 'section' | 'full-page' | 'minimal' = 'section') => {
    setCurrentSection(section)
    setProgress(loadingProgress)
    setVariant(loadingVariant)
    setIsLoading(true)
  }

  const hideSectionLoading = () => {
    setIsLoading(false)
    setProgress(undefined)
  }

  const updateSectionProgress = (newProgress: number) => {
    setProgress(newProgress)
  }

  const switchSection = (section: string) => {
    setCurrentSection(section)
  }

  return {
    isLoading,
    currentSection,
    progress,
    variant,
    showSectionLoading,
    hideSectionLoading,
    updateSectionProgress,
    switchSection
  }
}

// Utility component for wrapping landing page sections with loading
export function LandingPageSection({
  children,
  sectionName,
  isLoading = false,
  className = ""
}: {
  children: React.ReactNode
  sectionName: string
  isLoading?: boolean
  className?: string
}) {
  return (
    <div className={`relative ${className}`}>
      {isLoading ? (
        <LandingPageLoading
          isVisible={true}
          currentSection={sectionName}
          variant="section"
        />
      ) : (
        children
      )}
    </div>
  )
}
