"use client"

import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Zap, Cpu, Brain, Target } from 'lucide-react'

interface GlobalLoadingProps {
  isVisible: boolean
  message?: string
  progress?: number
  variant?: 'default' | 'minimal' | 'full'
}

interface QuantumParticle {
  id: number
  x: number
  y: number
  delay: number
  color: string
  size: number
}

const quantumColors = {
  cyan: '#22d3ee',
  purple: '#8b5cf6', 
  gold: '#fbbf24'
}

const loadingMessages = [
  'Initializing quantum consciousness...',
  'Calibrating neural pathways...',
  'Synchronizing quantum fields...',
  'Loading NanoVerse protocols...',
  'Establishing quantum connection...',
  'Preparing neural interface...'
]

const quantumIcons = [
  { Icon: Zap, color: quantumColors.cyan, label: 'Neural Core' },
  { Icon: Cpu, color: quantumColors.purple, label: 'Quantum AI' },
  { Icon: Brain, color: quantumColors.gold, label: 'Consciousness' },
  { Icon: Target, color: quantumColors.cyan, label: 'Precision' }
]

export function GlobalLoading({ 
  isVisible, 
  message, 
  progress,
  variant = 'default' 
}: GlobalLoadingProps) {
  const [currentMessage, setCurrentMessage] = useState(message || loadingMessages[0])
  const [particles, setParticles] = useState<QuantumParticle[]>([])
  const [currentIconIndex, setCurrentIconIndex] = useState(0)

  // Generate quantum particles
  useEffect(() => {
    if (!isVisible) return

    const generateParticles = () => {
      const newParticles: QuantumParticle[] = []
      const colors = Object.values(quantumColors)
      
      for (let i = 0; i < 20; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          delay: Math.random() * 3,
          color: colors[Math.floor(Math.random() * colors.length)],
          size: Math.random() * 4 + 2
        })
      }
      setParticles(newParticles)
    }

    generateParticles()
  }, [isVisible])

  // Cycle through messages
  useEffect(() => {
    if (!isVisible || message) return

    const interval = setInterval(() => {
      setCurrentMessage(prev => {
        const currentIndex = loadingMessages.indexOf(prev)
        const nextIndex = (currentIndex + 1) % loadingMessages.length
        return loadingMessages[nextIndex]
      })
    }, 2000)

    return () => clearInterval(interval)
  }, [isVisible, message])

  // Cycle through icons
  useEffect(() => {
    if (!isVisible) return

    const interval = setInterval(() => {
      setCurrentIconIndex(prev => (prev + 1) % quantumIcons.length)
    }, 1500)

    return () => clearInterval(interval)
  }, [isVisible])

  if (variant === 'minimal') {
    return (
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
          >
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 border-2 border-neural-cyan border-t-transparent rounded-full quantum-loader-ring" />
              <span className="text-white font-space-grotesk text-sm">
                {currentMessage}
              </span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    )
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center"
          style={{
            background: 'linear-gradient(135deg, #0f0f23 0%, #1e1b4b 50%, #0f0f23 100%)'
          }}
        >
          {/* Consciousness wave background */}
          <div className="absolute inset-0 consciousness-wave opacity-30" />
          
          {/* Quantum particles */}
          <div className="absolute inset-0 overflow-hidden">
            {particles.map((particle) => (
              <motion.div
                key={particle.id}
                className="absolute rounded-full quantum-particle"
                style={{
                  left: `${particle.x}%`,
                  top: `${particle.y}%`,
                  width: `${particle.size}px`,
                  height: `${particle.size}px`,
                  backgroundColor: particle.color,
                  boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`
                }}
                animate={{
                  opacity: [0.3, 1, 0.3],
                  scale: [0.8, 1.2, 0.8],
                  x: [0, Math.random() * 20 - 10, 0],
                  y: [0, Math.random() * 20 - 10, 0]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: particle.delay,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>

          {/* Main loading content */}
          <div className="relative z-10 text-center">
            {/* Quantum energy rings */}
            <div className="relative mb-8">
              {/* Outer ring */}
              <motion.div
                className="w-32 h-32 rounded-full border-2 border-neural-cyan/30"
                animate={{ rotate: 360 }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              />
              
              {/* Middle ring */}
              <motion.div
                className="absolute inset-2 rounded-full border-2 border-quantum-purple/40"
                animate={{ rotate: -360 }}
                transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
              />
              
              {/* Inner ring */}
              <motion.div
                className="absolute inset-4 rounded-full border-2 border-quantum-gold/50"
                animate={{ rotate: 360 }}
                transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
              />

              {/* Central icon */}
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  key={currentIconIndex}
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  exit={{ scale: 0, rotate: 180 }}
                  transition={{ duration: 0.5 }}
                  className="w-12 h-12 rounded-full flex items-center justify-center quantum-glow"
                  style={{
                    background: `linear-gradient(135deg, ${quantumIcons[currentIconIndex].color}30, ${quantumIcons[currentIconIndex].color}10)`,
                    border: `2px solid ${quantumIcons[currentIconIndex].color}`,
                  }}
                >
                  <quantumIcons[currentIconIndex].Icon 
                    className="w-6 h-6" 
                    style={{ color: quantumIcons[currentIconIndex].color }}
                  />
                </motion.div>
              </div>
            </div>

            {/* Loading text */}
            <motion.div
              key={currentMessage}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.5 }}
              className="mb-6"
            >
              <h2 className="text-2xl font-orbitron font-bold bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent mb-2">
                NanoVerse Loading
              </h2>
              <p className="text-white/80 font-space-grotesk text-sm">
                {currentMessage}
              </p>
            </motion.div>

            {/* Progress bar (if provided) */}
            {typeof progress === 'number' && (
              <div className="w-64 mx-auto">
                <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold"
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
                <p className="text-white/60 text-xs mt-2 font-space-grotesk">
                  {Math.round(progress)}% Complete
                </p>
              </div>
            )}

            {/* Quantum status indicators */}
            <div className="flex justify-center gap-4 mt-8">
              {quantumIcons.map((item, index) => (
                <motion.div
                  key={index}
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                  animate={{
                    opacity: index === currentIconIndex ? [0.5, 1, 0.5] : 0.3,
                    scale: index === currentIconIndex ? [1, 1.2, 1] : 1
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: index === currentIconIndex ? Infinity : 0
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Hook for managing global loading state
export function useGlobalLoading() {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<string>()
  const [progress, setProgress] = useState<number>()

  const showLoading = (loadingMessage?: string, loadingProgress?: number) => {
    setMessage(loadingMessage)
    setProgress(loadingProgress)
    setIsLoading(true)
  }

  const hideLoading = () => {
    setIsLoading(false)
    setMessage(undefined)
    setProgress(undefined)
  }

  const updateProgress = (newProgress: number, newMessage?: string) => {
    setProgress(newProgress)
    if (newMessage) setMessage(newMessage)
  }

  return {
    isLoading,
    message,
    progress,
    showLoading,
    hideLoading,
    updateProgress
  }
}
