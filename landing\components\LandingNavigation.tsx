"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Zap, Sparkles, Target, Shield, Users, Gamepad2, Menu, X, ChevronDown } from 'lucide-react'

interface LandingNavigationProps {
  onLogin: () => void
}

export function LandingNavigation({ onLogin }: LandingNavigationProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)


  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.querySelector(`#${sectionId}`)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setIsMobileMenuOpen(false)
  }



  const navigationItems = [
    { id: 'decode', label: 'Features', icon: Sparkles, description: 'Game-like learning' },
    { id: 'community', label: 'Community', icon: Users, description: 'Connect & grow' },
    { id: 'safety', label: 'Safety', icon: Shield, description: 'Secure environment' },
    { id: 'map', label: 'Explore', icon: Gamepad2, description: 'Interactive journey' },
  ]

  return (
    <>
      {/* Mobile-First Navigation */}
      <motion.nav
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled ? 'py-2' : 'py-3'
        }`}
        style={{
          background: isScrolled
            ? 'linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(10, 15, 28, 0.95) 50%, rgba(0, 0, 0, 0.95) 100%)'
            : 'linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(10, 15, 28, 0.9) 50%, rgba(0, 0, 0, 0.8) 100%)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(34, 211, 238, 0.2)',
          boxShadow: isScrolled
            ? '0 8px 32px rgba(0, 0, 0, 0.4), 0 0 20px rgba(34, 211, 238, 0.15)'
            : '0 4px 16px rgba(0, 0, 0, 0.2), 0 0 10px rgba(34, 211, 238, 0.1)'
        }}
      >
        {/* Quantum wave background effect */}
        <div className="absolute inset-0 consciousness-wave opacity-10 pointer-events-none" />

        <div className="relative px-4 sm:px-6">
          <div className="flex items-center justify-between h-14 sm:h-16">
            {/* Mobile-First Logo */}
            <motion.div
              className="flex items-center gap-2 sm:gap-3 cursor-pointer group"
              onClick={() => scrollToSection('hero')}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Compact quantum logo */}
              <motion.div
                className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg flex items-center justify-center border border-neural-cyan/40 relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.2), rgba(139, 92, 246, 0.2))',
                  boxShadow: '0 0 15px rgba(34, 211, 238, 0.3)'
                }}
                animate={{
                  boxShadow: [
                    '0 0 15px rgba(34, 211, 238, 0.3)',
                    '0 0 25px rgba(34, 211, 238, 0.5)',
                    '0 0 15px rgba(34, 211, 238, 0.3)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <Zap className="w-4 h-4 sm:w-5 sm:h-5 text-neural-cyan" />
                </motion.div>
              </motion.div>

              <div className="flex flex-col">
                <motion.h1
                  className="text-lg sm:text-xl font-display font-bold bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent"
                  style={{
                    textShadow: '0 0 10px rgba(34, 211, 238, 0.5)'
                  }}
                >
                  NanoHero
                </motion.h1>
                <p className="hidden sm:block text-xs text-neural-cyan/70 font-caption leading-none">
                  Learn • Play • Hack Smart
                </p>
              </div>
            </motion.div>

            {/* Mobile-First Action Buttons */}
            <div className="flex items-center gap-2">
              {/* Quick Sign In - Always Visible */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              >
                <Button
                  onClick={onLogin}
                  size="sm"
                  className="relative px-3 py-2 text-xs sm:text-sm font-button text-white border border-neural-cyan/50 hover:border-neural-cyan transition-all duration-300 group overflow-hidden"
                  style={{
                    background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.15), rgba(139, 92, 246, 0.15))',
                    boxShadow: '0 0 15px rgba(34, 211, 238, 0.2)'
                  }}
                >
                  <span className="relative flex items-center gap-1 sm:gap-2">
                    <Target className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="hidden sm:inline">Sign In</span>
                    <span className="sm:hidden">In</span>
                  </span>

                  {/* Quantum sweep */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
                </Button>
              </motion.div>

              {/* Mobile Menu Button */}
              <motion.button
                className="w-10 h-10 sm:w-12 sm:h-12 rounded-lg border border-neural-cyan/40 flex items-center justify-center text-neural-cyan hover:bg-neural-cyan/10 transition-all duration-300 relative overflow-hidden group"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                whileTap={{ scale: 0.95 }}
                style={{
                  background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.1), rgba(139, 92, 246, 0.1))',
                  boxShadow: '0 0 10px rgba(34, 211, 238, 0.2)'
                }}
              >
                {/* Quantum glow on hover */}
                <motion.div
                  className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                    background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.2), rgba(139, 92, 246, 0.2))',
                    boxShadow: '0 0 20px rgba(34, 211, 238, 0.3)'
                  }}
                />

                <AnimatePresence mode="wait">
                  {isMobileMenuOpen ? (
                    <motion.div
                      key="close"
                      initial={{ rotate: -90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: 90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="relative z-10"
                    >
                      <X className="w-5 h-5 sm:w-6 sm:h-6" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="menu"
                      initial={{ rotate: 90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: -90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="relative z-10"
                    >
                      <Menu className="w-5 h-5 sm:w-6 sm:h-6" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>
            </div>
          </div>
        </div>
      </motion.nav>

      {/* Mobile-First Full Screen Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
              onClick={() => setIsMobileMenuOpen(false)}
            />

            {/* Mobile Menu Panel */}
            <motion.div
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="fixed top-16 sm:top-20 left-4 right-4 z-50 max-h-[calc(100vh-6rem)] overflow-y-auto"
            >
              <div
                className="rounded-2xl sm:rounded-3xl overflow-hidden border border-neural-cyan/30"
                style={{
                  background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(10, 15, 28, 0.95) 50%, rgba(0, 0, 0, 0.95) 100%)',
                  backdropFilter: 'blur(20px)',
                  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.5), 0 0 30px rgba(34, 211, 238, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                }}
              >
                {/* Quantum wave overlay */}
                <div className="absolute inset-0 consciousness-wave opacity-20 pointer-events-none" />

                <div className="relative p-4 sm:p-6">
                  {/* Menu Header */}
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="text-center mb-6 sm:mb-8"
                  >
                    <h2 className="text-xl sm:text-2xl font-orbitron font-bold bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent mb-2">
                      Explore NanoVerse
                    </h2>
                    <p className="text-sm text-white/60 font-space-grotesk">
                      Choose your quantum learning path
                    </p>
                  </motion.div>

                  {/* Mobile Navigation Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6">
                    {navigationItems.map((item, index) => (
                      <motion.button
                        key={item.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * (index + 1), duration: 0.4 }}
                        onClick={() => scrollToSection(item.id)}
                        className="group relative p-4 sm:p-5 rounded-xl sm:rounded-2xl text-left transition-all duration-300 border border-neural-cyan/20 hover:border-neural-cyan/50 overflow-hidden"
                        style={{
                          background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.08), rgba(139, 92, 246, 0.08))'
                        }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {/* Quantum glow effect */}
                        <motion.div
                          className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                          style={{
                            background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.15), rgba(139, 92, 246, 0.15))',
                            boxShadow: '0 0 20px rgba(34, 211, 238, 0.2)'
                          }}
                        />

                        <div className="relative flex items-start gap-3 sm:gap-4">
                          <motion.div
                            className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl border border-neural-cyan/30 flex items-center justify-center flex-shrink-0"
                            style={{
                              background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.2), rgba(139, 92, 246, 0.2))'
                            }}
                            whileHover={{ rotate: 360, scale: 1.1 }}
                            transition={{ duration: 0.5 }}
                          >
                            <item.icon className="w-5 h-5 sm:w-6 sm:h-6 text-neural-cyan" />
                          </motion.div>

                          <div className="flex-1 min-w-0">
                            <h3 className="text-base sm:text-lg font-space-grotesk font-semibold text-white group-hover:text-neural-cyan transition-colors duration-300 mb-1">
                              {item.label}
                            </h3>
                            <p className="text-xs sm:text-sm text-white/60 group-hover:text-white/80 transition-colors duration-300">
                              {item.description}
                            </p>
                          </div>

                          <ChevronDown className="w-4 h-4 text-neural-cyan/50 group-hover:text-neural-cyan transform group-hover:translate-x-1 transition-all duration-300 flex-shrink-0 mt-1" />
                        </div>

                        {/* Quantum sweep effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-neural-cyan/10 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
                      </motion.button>
                    ))}
                  </div>

                  {/* Enhanced Mobile Sign In */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="space-y-3"
                  >
                    <Button
                      onClick={onLogin}
                      className="w-full py-4 sm:py-5 text-base sm:text-lg font-space-grotesk font-bold text-white border-2 border-neural-cyan/50 hover:border-neural-cyan transition-all duration-300 relative overflow-hidden group"
                      style={{
                        background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.2), rgba(139, 92, 246, 0.2))',
                        boxShadow: '0 0 25px rgba(34, 211, 238, 0.3)'
                      }}
                    >
                      {/* Enhanced glow effect */}
                      <motion.div
                        className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        style={{
                          background: 'linear-gradient(135deg, rgba(34, 211, 238, 0.3), rgba(139, 92, 246, 0.3))',
                          boxShadow: '0 0 40px rgba(34, 211, 238, 0.4)'
                        }}
                      />

                      <span className="relative flex items-center justify-center gap-3">
                        <Target className="w-5 h-5 sm:w-6 sm:h-6" />
                        <span>Enter the NanoVerse</span>
                        <Sparkles className="w-4 h-4 sm:w-5 sm:h-5" />
                      </span>

                      {/* Quantum sweep */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
                    </Button>

                    <p className="text-center text-xs sm:text-sm text-white/50 font-space-grotesk">
                      Join thousands of quantum learners
                    </p>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}
