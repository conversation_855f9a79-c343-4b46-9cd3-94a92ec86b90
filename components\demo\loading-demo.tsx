"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { LandingPageLoading, LandingPageSection, useLandingPageLoading } from '@/components/ui/global-loading'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export function LoadingDemo() {
  const [showFullPage, setShowFullPage] = useState(false)
  const [showSection, setShowSection] = useState(false)
  const [showMinimal, setShowMinimal] = useState(false)
  const [currentSection, setCurrentSection] = useState('hero')
  const [progress, setProgress] = useState(0)

  const sections = [
    'hero',
    'features', 
    'decode',
    'journey',
    'byteverse',
    'community',
    'testimonials',
    'cta'
  ]

  const handleProgressDemo = () => {
    setProgress(0)
    setShowSection(true)
    
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setTimeout(() => setShowSection(false), 1000)
          return 100
        }
        return prev + 10
      })
    }, 200)
  }

  const handleSectionCycle = () => {
    setShowSection(true)
    let currentIndex = 0
    
    const interval = setInterval(() => {
      if (currentIndex >= sections.length) {
        clearInterval(interval)
        setShowSection(false)
        return
      }
      
      setCurrentSection(sections[currentIndex])
      currentIndex++
    }, 1500)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-dark via-space-blue to-space-dark p-8">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-orbitron font-bold bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold bg-clip-text text-transparent mb-4">
            Quantum Loading Animation Demo
          </h1>
          <p className="text-white/80 font-space-grotesk text-lg">
            Experience the quantum-themed loading animations for NanoVerse
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Full Page Loading */}
          <Card className="quantum-glass border-neural-cyan/30">
            <CardHeader>
              <CardTitle className="text-white font-orbitron">Full Page Loading</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-white/70 mb-4 font-space-grotesk text-sm">
                Complete quantum experience with particles, energy rings, and cycling icons.
              </p>
              <Button 
                onClick={() => {
                  setShowFullPage(true)
                  setTimeout(() => setShowFullPage(false), 5000)
                }}
                className="w-full bg-gradient-to-r from-neural-cyan to-quantum-purple hover:from-quantum-purple hover:to-neural-cyan"
              >
                Show Full Page
              </Button>
            </CardContent>
          </Card>

          {/* Section Loading */}
          <Card className="quantum-glass border-quantum-purple/30">
            <CardHeader>
              <CardTitle className="text-white font-orbitron">Section Loading</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-white/70 mb-4 font-space-grotesk text-sm">
                Perfect for individual landing page sections with quantum particles.
              </p>
              <Button 
                onClick={() => {
                  setShowSection(true)
                  setTimeout(() => setShowSection(false), 3000)
                }}
                className="w-full bg-gradient-to-r from-quantum-purple to-quantum-gold hover:from-quantum-gold hover:to-quantum-purple"
              >
                Show Section
              </Button>
            </CardContent>
          </Card>

          {/* Minimal Loading */}
          <Card className="quantum-glass border-quantum-gold/30">
            <CardHeader>
              <CardTitle className="text-white font-orbitron">Minimal Loading</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-white/70 mb-4 font-space-grotesk text-sm">
                Quick and subtle for transitions and data loading.
              </p>
              <Button 
                onClick={() => {
                  setShowMinimal(true)
                  setTimeout(() => setShowMinimal(false), 2000)
                }}
                className="w-full bg-gradient-to-r from-quantum-gold to-neural-cyan hover:from-neural-cyan hover:to-quantum-gold"
              >
                Show Minimal
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Progress Demo */}
          <Card className="quantum-glass border-neural-cyan/30">
            <CardHeader>
              <CardTitle className="text-white font-orbitron">Progress Loading</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-white/70 mb-4 font-space-grotesk text-sm">
                Loading with progress tracking for file uploads or data processing.
              </p>
              <Button 
                onClick={handleProgressDemo}
                className="w-full bg-gradient-to-r from-neural-cyan via-quantum-purple to-quantum-gold"
              >
                Demo Progress
              </Button>
            </CardContent>
          </Card>

          {/* Section Cycling Demo */}
          <Card className="quantum-glass border-quantum-purple/30">
            <CardHeader>
              <CardTitle className="text-white font-orbitron">Section Cycling</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-white/70 mb-4 font-space-grotesk text-sm">
                See how different sections display with contextual messages.
              </p>
              <Button 
                onClick={handleSectionCycle}
                className="w-full bg-gradient-to-r from-quantum-purple via-quantum-gold to-neural-cyan"
              >
                Cycle Sections
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Usage Example */}
        <Card className="quantum-glass border-white/20 mt-8">
          <CardHeader>
            <CardTitle className="text-white font-orbitron">Usage Example</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-white/80 font-space-grotesk">
              <p className="mb-4">Here's how to use the loading animation in your components:</p>
              <pre className="bg-black/50 p-4 rounded-lg text-sm overflow-x-auto">
{`// For section loading
<LandingPageSection 
  sectionName="hero" 
  isLoading={isLoading}
>
  <YourComponent />
</LandingPageSection>

// Direct usage
<LandingPageLoading 
  isVisible={true}
  currentSection="features"
  variant="section"
  progress={50}
/>

// With hook
const { showSectionLoading, hideSectionLoading } = useLandingPageLoading()

// Show loading
showSectionLoading('decode', 0, 'section')

// Hide loading
hideSectionLoading()`}
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Loading Overlays */}
      <LandingPageLoading 
        isVisible={showFullPage}
        currentSection={currentSection}
        variant="full-page"
      />

      <LandingPageLoading 
        isVisible={showSection}
        currentSection={currentSection}
        variant="section"
        progress={progress > 0 ? progress : undefined}
      />

      <LandingPageLoading 
        isVisible={showMinimal}
        currentSection="demo"
        variant="minimal"
      />
    </div>
  )
}
